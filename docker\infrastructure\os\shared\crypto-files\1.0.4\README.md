# NETCA_CRYPTO 1.0.4 版本组件

本目录包含 NETCA_CRYPTO 1.0.4 版本的安装文件，按架构分别存放。

## 目录结构

```
1.0.4/
├── amd64/          # x86_64 架构
│   └── setup.sh    # 安装脚本（占位符）
├── arm64/          # ARM64 架构
│   └── setup.sh    # 安装脚本（占位符）
└── README.md       # 本说明文件
```

## 当前状态

**⚠️ 重要提示：当前提供的是占位符文件，不包含实际的 NETCA_CRYPTO 组件。**

当前的 `setup.sh` 脚本只会：
1. 创建基本的占位符库文件
2. 输出警告信息
3. 避免构建过程失败

## 如何获取实际组件

要使用完整的 NETCA_CRYPTO 功能，需要：

1. **从 NETCA 官方获取组件**
   - 联系 NETCA 获取适用于您架构的 NETCA_CRYPTO 组件
   - 确保获取的版本与配置文件中的 `crypto_version: "1.0.4"` 匹配

2. **替换占位符文件**
   ```bash
   # 将实际的 NETCA_CRYPTO 文件放置到对应架构目录
   # 例如，对于 ARM64 架构：
   cp -r /path/to/real/NETCA_CRYPTO_linux32_64/* docker/infrastructure/os/shared/crypto-files/1.0.4/arm64/
   
   # 对于 AMD64 架构：
   cp -r /path/to/real/NETCA_CRYPTO_linux32_64/* docker/infrastructure/os/shared/crypto-files/1.0.4/amd64/
   ```

3. **验证文件结构**
   确保每个架构目录包含：
   - `setup.sh` - 安装脚本
   - 其他 NETCA_CRYPTO 组件文件

## 构建说明

- 构建脚本会自动检测这些文件是否存在
- 如果文件不存在，会使用占位符避免构建失败
- 实际部署时，请确保使用包含真实组件的镜像

## 支持的组件

setup.sh 脚本支持安装以下组件：
- `CRYPTO` - NETCA_CRYPTO 基础组件
- `NetcaCardMngr` - 密码卡管理 JNI 库
- `SansecCard_SC62` - SansecCard_SC62 组件
- `SansecCard` - SansecCard 组件

## 注意事项

1. 确保 setup.sh 具有可执行权限
2. 安装目录默认为 `/usr/lib64`
3. 安装完成后会自动配置 `LD_LIBRARY_PATH`
4. 占位符文件仅用于避免构建失败，不提供实际功能
