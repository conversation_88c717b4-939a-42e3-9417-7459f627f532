#!/bin/bash
# 操作系统Docker镜像构建脚本

set -e

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取项目根目录
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# 定义目录路径
LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
BUILD_LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
CONFIG_FILE="${PROJECT_ROOT}/docker/scripts/build/config/harbor.conf"
BASE_DIR="${PROJECT_ROOT}/docker/infrastructure/os"
VERSION_FILE="${SCRIPT_DIR}/versions/os_versions.json"

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 设置Harbor仓库地址（优先级：命令行参数 > 环境变量 > 配置文件 > 默认值）
# 首先检查是否存在配置文件
if [ -f "$CONFIG_FILE" ]; then
    # 从配置文件读取
    source "$CONFIG_FILE"
fi

# 然后检查环境变量（覆盖配置文件）
HARBOR_REGISTRY=${HARBOR_REGISTRY:-${HARBOR_REGISTRY_DEFAULT:-""}}
HARBOR_USER=${HARBOR_USER:-${HARBOR_USER_DEFAULT:-""}}
HARBOR_PASSWORD=${HARBOR_PASSWORD:-${HARBOR_PASSWORD_DEFAULT:-""}}

# 显示Harbor配置信息函数
show_harbor_config() {
    if [ -z "$HARBOR_REGISTRY" ]; then
        echo -e "${RED}错误: Harbor仓库地址未配置${NC}"
        echo "请通过以下方式之一设置Harbor仓库地址:"
        echo "  1. 设置环境变量: export HARBOR_REGISTRY=your-harbor-registry:port"
        echo "  2. 创建配置文件: ${CONFIG_FILE}"
        echo "     配置文件内容示例: HARBOR_REGISTRY_DEFAULT=\"your-harbor-registry:port\""
        echo "  3. 使用命令行参数: --harbor-registry=your-harbor-registry:port"
        return 1
    else
        echo -e "${GREEN}Harbor仓库地址: $HARBOR_REGISTRY${NC}"
        if [ -n "$HARBOR_USER" ] && [ -n "$HARBOR_PASSWORD" ]; then
            echo "Harbor凭证: 已配置"
        else
            echo "Harbor凭证: 未配置（可能无法推送镜像）"
        fi
        return 0
    fi
}

# 帮助信息
function show_help() {
    echo -e "${GREEN}操作系统Docker镜像构建工具${NC}"
    echo "用法: $0 [选项] <os_type> <version> [arch]"
    echo ""
    echo "参数:"
    echo "  os_type  - 操作系统类型 (ubuntu, alpine, centos, rhel, openeuler, kylin)"
    echo "  version  - 操作系统版本 (如: 22.04, 3.17, 7)"
    echo "  arch     - 可选: 架构 (amd64, arm64), 默认: amd64"
    echo ""
    echo "选项:"
    echo "  -h, --help                     显示帮助信息"
    echo "  --harbor-registry=<地址>       指定Harbor仓库地址 (如: registry.example.com:1443)"
    echo "  --harbor-user=<用户名>         指定Harbor用户名"
    echo "  --harbor-password=<密码>       指定Harbor密码"
    echo "  --debug                        启用调试模式，保留临时文件"
    echo "  --output-dir=<目录>            指定生成的Dockerfile输出目录"
    echo "  --no-push                      构建镜像但不推送到仓库
  --no-cache                     强制不使用Docker缓存重新构建"
    echo "  --install-card-mgr             安装密码卡管理的JNI库"
    echo "  --install-sc62                 安装SansecCard_SC62组件"
    echo "  --install-sc34                 安装SansecCard_SC34组件"
    echo "  --install-all-cards            安装所有支持的密码卡组件"
    echo ""
    echo "示例:"
    echo "  $0 ubuntu 22.04"
    echo "  $0 alpine 3.17 arm64"
    echo "  $0 --harbor-registry=192.168.1.100:1443 ubuntu 22.04"
    echo "  $0 ubuntu 22.04 --install-card-mgr --install-sc62"
    echo "  $0 ubuntu 22.04 --install-all-cards"
    echo "  $0 ubuntu 22.04 --no-cache --debug"
    exit 1
}

# 解析命令行参数
parse_args() {
    NO_PUSH=false
    DEBUG=false
    NO_CACHE=false
    OUTPUT_DIR=""
    INSTALL_CARD_MNGR=false
    INSTALL_SC62=false
    INSTALL_SC34=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --harbor-registry=*)
                HARBOR_REGISTRY="${1#*=}"
                shift
                ;;
            --harbor-registry)
                HARBOR_REGISTRY="$2"
                shift 2
                ;;
            --harbor-user=*)
                HARBOR_USER="${1#*=}"
                shift
                ;;
            --harbor-user)
                HARBOR_USER="$2"
                shift 2
                ;;
            --harbor-password=*)
                HARBOR_PASSWORD="${1#*=}"
                shift
                ;;
            --harbor-password)
                HARBOR_PASSWORD="$2"
                shift 2
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --output-dir=*)
                OUTPUT_DIR="${1#*=}"
                shift
                ;;
            --output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --no-push)
                NO_PUSH=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --install-card-mgr)
                INSTALL_CARD_MNGR=true
                shift
                ;;
            --install-sc62)
                INSTALL_SC62=true
                shift
                ;;
            --install-sc34)
                INSTALL_SC34=true
                shift
                ;;
            --install-all-cards)
                INSTALL_CARD_MNGR=true
                INSTALL_SC62=true
                INSTALL_SC34=true
                shift
                ;;
            -*)
                echo "错误: 未知选项 $1"
                show_help
                exit 1
                ;;
            *)
                # 处理位置参数
                if [ -z "$OS_TYPE" ]; then
                    OS_TYPE="$1"
                elif [ -z "$VERSION" ]; then
                    VERSION="$1"
                elif [ -z "$ARCH" ]; then
                    ARCH="$1"
                else
                    echo "错误: 参数过多"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必要参数
    if [ -z "$OS_TYPE" ] || [ -z "$VERSION" ]; then
        echo "错误: 需要指定操作系统类型和版本"
        show_help
        exit 1
    fi
}

# 登录Harbor仓库
login_registry() {
    if [ -z "$HARBOR_REGISTRY" ]; then
        echo -e "${RED}错误: Harbor仓库地址未配置${NC}"
        return 1
    fi
    
    if [ -z "${HARBOR_USER}" ] || [ -z "${HARBOR_PASSWORD}" ]; then
        echo "警告: Harbor用户名或密码未配置，镜像可能无法推送"
        return 1
    fi

    echo "正在登录Harbor仓库: ${HARBOR_REGISTRY}"
    printf "%s" "${HARBOR_PASSWORD}" | docker login "${HARBOR_REGISTRY}" -u "${HARBOR_USER}" --password-stdin
    return $?
}

# 构建镜像
build_os() {
    local os_type=$1
    local version=$2
    local arch=$3
    
    echo -e "${GREEN}开始构建 $os_type $version for $arch${NC}"
    
    # 检查OS目录
    OS_DIR="$BASE_DIR/$os_type"
    VERSION_FILE="${SCRIPT_DIR}/versions/os_versions.json"
    TEMPLATE_FILE="$OS_DIR/Dockerfile.template"
    OUTPUT_DIR=${OUTPUT_DIR:-"$BASE_DIR/build"}
    SETUP_DIR="${PROJECT_ROOT}/setup/images/infrastructure/os/$os_type"
    
    # 检查目录和文件是否存在
    if [ ! -d "$OS_DIR" ]; then
        echo -e "${RED}错误: 不支持的操作系统类型 '$os_type'${NC}"
        exit 1
    fi

    if [ ! -f "$VERSION_FILE" ]; then
        echo -e "${RED}错误: 版本配置文件不存在: $VERSION_FILE${NC}"
        exit 1
    fi

    if [ ! -f "$TEMPLATE_FILE" ]; then
        echo -e "${RED}错误: Dockerfile模板不存在: $TEMPLATE_FILE${NC}"
        exit 1
    fi

    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 解析版本信息
    CODENAME=$(jq -r ".versions[] | select(.version == \"$version\") | .codename" "$VERSION_FILE" 2>/dev/null)
    PACKAGES=$(jq -r ".versions[] | select(.version == \"$version\") | .packages" "$VERSION_FILE" 2>/dev/null)
    CRYPTO_VERSION=$(jq -r ".versions[] | select(.version == \"$version\") | .crypto_version" "$VERSION_FILE" 2>/dev/null || jq -r ".default_crypto_version" "$VERSION_FILE")
    VALID_VERSION=$(jq -r ".versions[] | select(.version == \"$version\") | .version" "$VERSION_FILE" 2>/dev/null)
    
    if [ -z "$VALID_VERSION" ]; then
        echo -e "${RED}错误: 版本 '$version' 不在支持列表中${NC}"
        echo -e "${YELLOW}支持的版本:${NC}"
        jq -r '.versions[].version' "$VERSION_FILE" | sed 's/^/  - /'
        exit 1
    fi
    
    # 检查架构支持
    VALID_ARCH=$(jq -r ".versions[] | select(.version == \"$version\") | .architectures | contains([\"$arch\"])" "$VERSION_FILE" 2>/dev/null)
    if [ "$VALID_ARCH" != "true" ]; then
        echo -e "${RED}错误: 架构 '$arch' 不支持版本 '$version'${NC}"
        echo -e "${YELLOW}支持的架构:${NC}"
        jq -r ".versions[] | select(.version == \"$version\") | .architectures[]" "$VERSION_FILE" | sed 's/^/  - /'
        exit 1
    fi
    
    # 创建临时构建目录
    local build_dir=$(mktemp -d)
    cp "$TEMPLATE_FILE" "$build_dir/Dockerfile"
    
    # 复制必要的资源文件
    if [ -d "$BASE_DIR/shared/crypto-files" ]; then
        echo -e "${YELLOW}复制加密组件文件...${NC}"

        # 创建目标目录结构
        mkdir -p "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch"
        mkdir -p "$build_dir/shared/crypto-files/kylin"

        # 复制 NETCA_CRYPTO 文件（如果存在）
        local crypto_source="$BASE_DIR/shared/crypto-files/$CRYPTO_VERSION/$arch"
        if [ -d "$crypto_source" ]; then
            echo "复制 NETCA_CRYPTO 文件: $crypto_source -> $build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/"
            cp -r "$crypto_source/"* "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/"
        else
            echo -e "${YELLOW}警告: NETCA_CRYPTO 文件不存在: $crypto_source${NC}"
            echo -e "${YELLOW}将创建空的 NETCA_CRYPTO 目录以避免构建失败${NC}"
            # 创建一个空的 setup.sh 脚本以避免构建失败
            echo '#!/bin/bash' > "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"
            echo 'echo "NETCA_CRYPTO 组件未提供，跳过安装"' >> "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"
            chmod +x "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"
        fi

        # 复制 Kylin 专用的加密文件
        if [ -d "$BASE_DIR/shared/crypto-files/kylin" ]; then
            echo "复制 Kylin 加密库文件..."
            cp -r "$BASE_DIR/shared/crypto-files/kylin/"* "$build_dir/shared/crypto-files/kylin/"
        else
            echo -e "${YELLOW}警告: Kylin 加密库文件不存在，将创建空目录${NC}"
            # 创建空目录以避免 COPY 失败
            touch "$build_dir/shared/crypto-files/kylin/.keep"
        fi
    else
        echo -e "${YELLOW}警告: 加密组件目录不存在: $BASE_DIR/shared/crypto-files${NC}"
        echo -e "${YELLOW}将创建空的加密组件目录结构${NC}"

        # 创建空的目录结构以避免构建失败
        mkdir -p "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch"
        mkdir -p "$build_dir/shared/crypto-files/kylin"

        # 创建空的 setup.sh 脚本
        echo '#!/bin/bash' > "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"
        echo 'echo "NETCA_CRYPTO 组件未提供，跳过安装"' >> "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"
        chmod +x "$build_dir/shared/crypto-files/$CRYPTO_VERSION/$arch/setup.sh"

        # 创建空的 kylin 目录
        touch "$build_dir/shared/crypto-files/kylin/.keep"
    fi
    
    # 替换模板变量
    sed -i "s/{{VERSION}}/$version/g" "$build_dir/Dockerfile"
    [ ! -z "$CODENAME" ] && sed -i "s/{{CODENAME}}/$CODENAME/g" "$build_dir/Dockerfile"
    [ ! -z "$PACKAGES" ] && sed -i "s/{{PACKAGES}}/$PACKAGES/g" "$build_dir/Dockerfile"

    # 调试模式下显示替换后的包列表
    if [ "$DEBUG" = "true" ]; then
        echo -e "${YELLOW}调试信息: 将要安装的包列表:${NC}"
        echo "  PACKAGES: $PACKAGES"
        echo -e "${YELLOW}生成的 Dockerfile 中的包安装部分:${NC}"
        grep -A 20 "apt-get install" "$build_dir/Dockerfile" | head -25
    fi
    
    # 从配置文件中获取命名空间和仓库
    local namespace=$(jq -r '.build_defaults.namespace // "btit"' "$VERSION_FILE")
    local repository=$(jq -r '.build_defaults.repository // "infra/os"' "$VERSION_FILE")
    
    # 如果repository不包含/，则将namespace和repository结合
    if [[ "$repository" != */* ]]; then
        repository="$namespace/$repository"
    else
        # 如果已经包含/，则使用完整路径
        repository="$namespace/$repository"
    fi
    
    # 构建标签名称 - 添加操作系统类型到仓库路径
    local image_tag="$HARBOR_REGISTRY/$repository/$os_type:$version"
    
    # 添加加密卡功能后缀
    if [ "$INSTALL_CARD_MNGR" = "true" ] || [ "$INSTALL_SC62" = "true" ] || [ "$INSTALL_SC34" = "true" ]; then
        image_tag="${image_tag}-crypto"
        
        # 如果安装了特定的卡
        if [ "$INSTALL_SC62" = "true" ]; then
            image_tag="${image_tag}-sc62"
        fi
        if [ "$INSTALL_SC34" = "true" ]; then
            image_tag="${image_tag}-sc34"
        fi
    fi
    
    echo -e "${GREEN}已生成Dockerfile${NC}"
    
    # 如果指定了输出目录，保存生成的Dockerfile
    if [ -n "$OUTPUT_DIR" ]; then
        local dockerfile_output="$OUTPUT_DIR/Dockerfile.$os_type-$version-$arch"
        cp "$build_dir/Dockerfile" "$dockerfile_output"
        echo -e "${GREEN}已保存Dockerfile到: $dockerfile_output${NC}"
    fi
    
    # 构建镜像
    echo -e "${GREEN}开始构建镜像: $image_tag${NC}"

    # 构建参数数组
    local build_args=(
        "--platform" "linux/$arch"
        "--build-arg" "CRYPTO_VERSION=$CRYPTO_VERSION"
        "--build-arg" "ARCH=$arch"
        "--build-arg" "OS_TYPE=$os_type"
        "--build-arg" "INSTALL_CARD_MNGR=$INSTALL_CARD_MNGR"
        "--build-arg" "INSTALL_SC62=$INSTALL_SC62"
        "--build-arg" "INSTALL_SC34=$INSTALL_SC34"
        "-t" "$image_tag"
    )

    # 如果启用了调试模式或强制无缓存，添加 --no-cache 参数
    if [ "$DEBUG" = "true" ] || [ "$NO_CACHE" = "true" ]; then
        build_args+=("--no-cache")
        echo -e "${YELLOW}使用 --no-cache 构建（调试模式或强制无缓存）${NC}"
    fi

    # 添加详细输出
    if [ "$DEBUG" = "true" ]; then
        build_args+=("--progress=plain")
        echo -e "${YELLOW}启用详细构建输出${NC}"
    fi

    (
        cd "$build_dir"
        echo -e "${CYAN}执行构建命令: docker build ${build_args[*]} .${NC}"
        docker build "${build_args[@]}" .
    )
    
    # 构建结果
    local build_result=$?
    
    # 清理临时文件
    if [ "$DEBUG" != "true" ]; then
        rm -rf "$build_dir"
    else
        echo "调试模式: 保留临时构建目录 $build_dir"
    fi
    
    if [ $build_result -ne 0 ]; then
        echo -e "${RED}错误: 镜像构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}镜像构建完成: $image_tag${NC}"

    # 验证镜像中的关键工具是否可用
    echo -e "${YELLOW}验证镜像中的关键工具...${NC}"

    # 验证 file 命令
    if docker run --rm "$image_tag" which file >/dev/null 2>&1; then
        echo -e "${GREEN}✓ file 命令验证通过${NC}"
    else
        echo -e "${RED}✗ file 命令验证失败${NC}"
        echo -e "${YELLOW}尝试检查 file 包安装状态...${NC}"
        docker run --rm "$image_tag" dpkg -l | grep file || true
        echo -e "${YELLOW}警告: file 命令不可用，可能影响某些功能${NC}"
    fi

    # 验证其他关键工具
    for tool in curl wget vim; do
        if docker run --rm "$image_tag" which "$tool" >/dev/null 2>&1; then
            echo -e "${GREEN}✓ $tool 命令验证通过${NC}"
        else
            echo -e "${YELLOW}⚠ $tool 命令不可用${NC}"
        fi
    done

    echo -e "${GREEN}镜像验证完成${NC}"

    # 推送镜像到Harbor
    if [ "$NO_PUSH" != "true" ]; then
        echo -e "${GREEN}推送镜像到Harbor: $image_tag${NC}"
        docker push "$image_tag"
        
        if [ $? -ne 0 ]; then
            echo -e "${RED}错误: 推送镜像失败${NC}"
            exit 1
        fi
        
        echo -e "${GREEN}镜像已推送到Harbor${NC}"
    else
        echo -e "${YELLOW}已跳过推送镜像（--no-push选项）${NC}"
    fi
    
    # 保存镜像到文件
    mkdir -p "$SETUP_DIR"
    local image_file="$SETUP_DIR/$os_type-$version-$arch"
    
    # 添加加密卡功能后缀到文件名
    if [ "$INSTALL_CARD_MNGR" = "true" ] || [ "$INSTALL_SC62" = "true" ] || [ "$INSTALL_SC34" = "true" ]; then
        image_file="${image_file}-crypto"
        
        # 如果安装了特定的卡
        if [ "$INSTALL_SC62" = "true" ]; then
            image_file="${image_file}-sc62"
        fi
        if [ "$INSTALL_SC34" = "true" ]; then
            image_file="${image_file}-sc34"
        fi
    fi
    
    image_file="${image_file}.tar.gz"
    echo -e "${GREEN}正在保存镜像到: $image_file${NC}"
    docker save "$image_tag" | gzip > "$image_file"
    echo -e "${GREEN}镜像已保存到: $image_file${NC}"
    
    echo -e "${GREEN}$os_type $version for $arch 构建完成！${NC}"
    return 0
}

# 检查环境依赖
check_requirements() {
    # 检查Docker是否已安装
    if ! command -v docker >/dev/null 2>&1; then
        echo -e "${RED}错误: 未检测到Docker，请先安装Docker${NC}"
        exit 1
    fi
    
    # 检查jq是否已安装
    if ! command -v jq >/dev/null 2>&1; then
        echo -e "${RED}错误: 未检测到jq，请先安装jq${NC}"
        exit 1
    fi
}

# 加载版本配置
load_versions() {
    # 检查版本配置文件是否存在
    if [ ! -f "$VERSION_FILE" ]; then
        echo "错误: 版本配置文件不存在: $VERSION_FILE"
        exit 1
    fi
    
    # 检查是否可以解析JSON
    if ! jq '.' "$VERSION_FILE" > /dev/null 2>&1; then
        echo "错误: 版本配置文件不是有效的JSON格式: $VERSION_FILE"
        exit 1
    fi
    
    # 获取支持的版本列表
    VERSIONS=($(jq -r '.versions[].version' "$VERSION_FILE"))
    if [ ${#VERSIONS[@]} -eq 0 ]; then
        echo "错误: 未在配置文件中找到支持的Ubuntu版本"
        exit 1
    fi
    
    # 获取默认版本和架构
    DEFAULT_VERSION=$(jq -r '.default_version // "22.04"' "$VERSION_FILE")
    DEFAULT_ARCH=$(jq -r '.default_arch // "amd64"' "$VERSION_FILE")
    
    echo "已加载版本配置:"
    echo "  支持的Ubuntu版本: ${VERSIONS[*]}"
    echo "  默认版本: $DEFAULT_VERSION"
    echo "  默认架构: $DEFAULT_ARCH"
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 检查环境依赖
    check_requirements
    
    # 显示并验证Harbor配置
    if ! show_harbor_config; then
        # 如果设置了--no-push，允许不配置Harbor继续
        if [ "$NO_PUSH" != "true" ]; then
            exit 1
        else
            echo -e "${YELLOW}警告: Harbor未配置，但由于设置了--no-push，将继续构建${NC}"
        fi
    else
        # 登录Harbor（如果未设置--no-push）
        if [ "$NO_PUSH" != "true" ]; then
            login_registry || echo -e "${YELLOW}警告: Harbor登录失败，可能无法推送镜像${NC}"
        fi
    fi
    
    # 确保已指定架构，默认为amd64
    ARCH=${ARCH:-$(jq -r ".default_arch" "${SCRIPT_DIR}/versions/os_versions.json" 2>/dev/null || echo "amd64")}
    
    # 显示构建配置信息
    echo -e "${GREEN}构建配置:${NC}"
    echo "操作系统: $OS_TYPE"
    echo "版本: $VERSION"
    echo "架构: $ARCH"
    echo "调试模式: $DEBUG"
    echo "强制无缓存: $NO_CACHE"
    echo "推送镜像: $([ "$NO_PUSH" = "true" ] && echo "否" || echo "是")"
    echo "安装密码卡管理JNI库: $INSTALL_CARD_MNGR"
    echo "安装SansecCard_SC62: $INSTALL_SC62"
    echo "安装SansecCard_SC34: $INSTALL_SC34"
    
    # 构建OS镜像
    build_os "$OS_TYPE" "$VERSION" "$ARCH"
}

# 执行主函数
main "$@" 